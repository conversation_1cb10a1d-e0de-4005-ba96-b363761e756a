{"name": "HighFi", "version": "1.0.48", "scripts": {"dev": "vite", "buildx": "vue-tsc --noEmit && vite build", "build": "vite build", "serve": "vite preview"}, "dependencies": {"@pdfme/common": "^5.4.1", "@pdfme/generator": "^4.0.0", "@pdfme/schemas": "^5.4.1", "@types/lodash": "^4.14.182", "@types/marked": "^4.0.3", "@types/pdfmake": "^0.1.20", "apexcharts": "^3.33.2", "axios": "^0.21.1", "compressorjs": "^1.1.1", "events": "^3.3.0", "html-to-pdfmake": "^2.3.10", "js-sha256": "^0.9.0", "jspdf": "^2.5.1", "lodash": "^4.17.21", "marked": "^4.0.14", "md5": "^2.3.0", "moment": "^2.29.1", "papaparse": "^5.4.1", "pdf-lib": "^1.17.1", "pdfkit": "^0.17.1", "pdfmake": "^0.2.4", "qrcode.vue": "^3.4.1", "sass": "^1.49.9", "sweetalert2": "^11.22.2", "unenv": "^1.10.0", "vite-plugin-node-polyfills": "^0.22.0", "vue": "^3.0.5", "vue-i18n": "^9.1.6", "vue-router": "^4.0.8", "vue-upload-component": "^3.1.17", "vue3-apexcharts": "^1.4.1", "vue3-datepicker": "^0.2.5", "vue3-markdown-it": "^1.0.10", "xlsx": "^0.18.5"}, "devDependencies": {"@intlify/vite-plugin-vue-i18n": "^2.5.0", "@types/md5": "^2.3.1", "@types/node": "^22.15.17", "@vitejs/plugin-vue": "^1.2.2", "@vue/compiler-sfc": "^3.0.5", "autoprefixer": "^10.2.5", "idb-keyval": "^5.0.5", "postcss": "^8.3.0", "tailwindcss": "^2.1.2", "typescript": "^5.3.3", "vite": "^2.3.0", "vue-tsc": "^2.2.10"}}