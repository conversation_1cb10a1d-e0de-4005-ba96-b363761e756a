import {Store} from "./main"
import {USER_STORE_NAME} from "./store-names"
import md5 from 'md5'
import { getTickets, getTicket, createTicket, updateTicket, deleteTicket } from '../api'
interface Ticket extends Object {
    ticket: any
    ticketSuccess: string
    ticketError: string
    tickets: any
    ticketsSuccess: string
    ticketsError: string
    ticketCreate: any
    ticketCreateSuccess: string
    ticketCreateError: string
    ticketUpdate: any
    ticketUpdateSuccess: string
    ticketUpdateError: string
    ticketDelete: any
    ticketDeleteSuccess: string
    ticketDeleteError: string
}

class TicketStore extends Store<Ticket> {
    protected data(): Ticket {
        return {
            ticket: null,
            ticketSuccess: '',
            ticketError: '',
            tickets: null,
            ticketsSuccess: '',
            ticketsError: '',
            ticketCreate: null,
            ticketCreateSuccess: '',
            ticketCreateError: '',
            ticketUpdate: null,
            ticketUpdateSuccess: '',
            ticketUpdateError: '',
            ticketDelete: null,
            ticketDeleteSuccess: '',
            ticketDeleteError: ''
        }
    }
    ticketRequest () {
        this.state.ticket = null
        this.state.ticketSuccess = ''
        this.state.ticketError = ''
    }
    ticketsRequest () {
        this.state.tickets = null
        this.state.ticketsSuccess = ''
        this.state.ticketsError = ''
    }
    ticketCreateRequest () {
        this.state.ticketCreate = null
        this.state.ticketCreateSuccess = ''
        this.state.ticketCreateError = ''
    }
    ticketUpdateRequest () {
        this.state.ticketUpdate = null
        this.state.ticketUpdateSuccess = ''
        this.state.ticketUpdateError = ''
    }
    ticketDeleteRequest () {
        this.state.ticketDelete = null
        this.state.ticketDeleteSuccess = ''
        this.state.ticketDeleteError = ''
    }
    async getTicket (data: any)  {
        this.ticketRequest()
        return new Promise((resolve, reject) => {
            getTicket(data).then((res: any) => {
                this.state.ticket = res
                this.state.ticketSuccess = 'ticket.successLoadingTicket'
                this.state.ticketError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketSuccess = ''
                this.state.ticketError = 'ticket.errLoadingTicket'
            })
        })
    }
    async getTickets (data: any)  {
        this.ticketsRequest()
        return new Promise((resolve, reject) => {
            getTickets(data).then((res: any) => {
                this.state.tickets = res
                this.state.ticketsSuccess = 'ticket.successLoadingTickets'
                this.state.ticketsError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketsSuccess = ''
                this.state.ticketsError = 'ticket.errLoadingTickets'
                reject(err)
            })
        })
    }
    async createTicket (data: any)  {
        this.ticketCreateRequest()
        return new Promise((resolve, reject) => {
            createTicket(data).then((res: any) => {
                this.state.ticketCreate = {...res.data, "new": true}
                if (!this.state.tickets) {
                    this.state.tickets = {
                        total: 0,
                        data: []
                    }
                }
                if (this.state.tickets && this.state.tickets.data.length > 0) {
                    this.state.tickets.data.push(this.state.ticketCreate)
                    this.state.tickets.total ++
                }
                this.state.ticketCreateSuccess = 'ticket.createSuccess'
                this.state.ticketCreateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketCreateSuccess = ''
                this.state.ticketCreateError = 'ticket.createError'
                reject(err)
            })
        })
    }
    async updateTicket (data: any)  {
        this.ticketUpdateRequest()
        return new Promise((resolve, reject) => {
            if (data.form && data.form.password) {
                data.form.password = md5(data.form.password)
            }
            updateTicket(data).then((res: any) => {
                this.state.ticketUpdate = {...res.data, "new": true}
                if (this.state.tickets && this.state.tickets.data.length > 0) {
                    const p = this.state.tickets.data.filter((pp: any) => {
                        return pp.ID == this.state.ticketUpdate.id || pp.id == this.state.ticketUpdate.id
                    })
                    if (p.length > 0) {
                        const i = this.state.tickets.data.indexOf(p[0])
                        this.state.tickets.data.splice(i, 1, this.state.ticketUpdate)
                    }
                }

                this.state.ticketUpdateSuccess = 'ticket.updateSuccess'
                this.state.ticketUpdateError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketUpdateSuccess = ''
                this.state.ticketUpdateError = 'ticket.updateError'
                reject(err)
            })
        })
    }
    async deleteTicket (data: any)  {
        this.ticketDeleteRequest()
        return new Promise((resolve, reject) => {
            deleteTicket(data).then((res: any) => {
                this.state.ticketDelete = {...res.data, "delete": true}
                if (this.state.tickets && this.state.tickets.data.length > 0) {
                    const p = this.state.tickets.data.filter((pp: any) => {
                        return pp.ID == data.id || pp.id == data.id
                    })
                    if (p && p.length > 0) {
                        const i = this.state.tickets.data.indexOf(p[0])
                        if (i > -1) {
                            this.state.tickets.data.splice(i, 1)
                            this.state.tickets.total - 1
                        }
                    }
                }
                this.state.ticketDeleteSuccess = 'ticket.deleteSuccess'
                this.state.ticketDeleteError = ''
                resolve(res)
            }).catch((err: any) => {
                this.state.ticketDeleteSuccess = ''
                this.state.ticketDeleteError = 'ticket.deleteError'
                reject(err)
            })
        })
    }

}

export const ticketStore = new TicketStore(USER_STORE_NAME);
