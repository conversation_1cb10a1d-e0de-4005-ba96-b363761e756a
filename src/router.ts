import { createRouter, createWebHistory } from 'vue-router'
import { setI18nLanguage, loadLocaleMessages, SUPPORT_LOCALES } from './i18n'

import type { Router, RouteRecordRaw } from 'vue-router'
import type { I18n, Composer } from 'vue-i18n'

// layout
import headerComp from './layout/landing/index.vue'
import adminHeaderComp from './layout/auth/index.vue'

// auth pages
import Dashboard from './pages/auth/Dashboard.vue'
import Billings from './pages/auth/billings/index.vue'
import Prebills from './pages/auth/prebills/index.vue'
import TicketsCustomer from './pages/auth/ticketCustomer/index.vue'
import TicketsCRUD from './pages/auth/tickets/index.vue'
import Users from './pages/auth/users/index.vue'
import Buildings from './pages/auth/buildings/index.vue'
import SubscriptionGroups from './pages/auth/subscriptiongroups/index.vue'
import Plans from './pages/auth/plans/index.vue'
import Report from './pages/auth/report/index.vue'
import UpcomingReport from './pages/auth/report/upcomingbill.vue'
import JnxReport from './pages/auth/report/jnxreport.vue'
import ActiveUserReport from './pages/auth/report/activeuserreport.vue'
import UserReport from './pages/auth/report/userreport.vue'
import ZeroSpecialRateReport from './pages/auth/report/zerospecialratereport.vue'
import BillWithAgent from './pages/auth/report/billwithagent.vue'
import BillWithAgent2 from './pages/auth/report/billwithagent2.vue'
import BillByDate from './pages/auth/report/billbydates.vue'
import BillByPlan from './pages/auth/report/billbyplan.vue'
import SubPayReport from './pages/auth/report/subpayreport.vue'
import AdHocReport from './pages/auth/report/adhocreport.vue'
import Subscriptions from './pages/auth/subscriptions/index.vue'
import Subscriptionsl1 from './pages/auth/subscriptions-l1/index.vue'
import Techorder from './pages/auth/installation/index.vue'
import ActivationReport from './pages/auth/report/activationreport.vue'
import SubscriptionReport from './pages/auth/report/subscriptionreport.vue'
import AgentUserReport from './pages/auth/report/agentlistreport.vue'
import LatestBillReport from './pages/auth/report/latestbillreport.vue'
import TicketReport from './pages/auth/report/ticketreport.vue'
import TicketSummaryReport from './pages/auth/report/ticketsummary.vue'
import UpcomingFreeUsageReport from './pages/auth/report/UpcomingFreeUsageReport.vue'
import SalesSummaryReport from './pages/auth/report/salessummaryreport.vue'
import SubscriptionTroubleshoot from './pages/auth/report/subscriptiontroubleshoot.vue'
import InvalidIcUserReport from './pages/auth/report/invalidicuserlistreport.vue'

import AdminPanel from './pages/admin/index.vue'
import IDSync from './pages/admin/idsync.vue'
import ImportSubscribers from './pages/admin/importsubscribers.vue'
import PrintInstallOrder from './pages/admin/printInstallOrder.vue'
import PaymentBulk from './pages/admin/paymentbulk.vue'
import EmailTools from './pages/admin/emailtools.vue'

// customer
import Faq from './pages/auth/Faq.vue'
import Settings from './pages/auth/Settings.vue'

// guides
import Guides from './pages/auth/guides/index.vue'
import ConsolidateGuide from './pages/auth/guides/ConsolidateGuide.vue'
import SubscriptionFilterGuide from './pages/auth/guides/SubscriptionFilterGuide.vue'
import SyncBillingGuide from './pages/auth/guides/SyncBillingGuide.vue'
import SubscriptionTroubleshootGuide from './pages/auth/guides/SubscriptionTroubleshootGuide.vue'
import CustomerGuide from './pages/auth/guides/CustomerGuide.vue'
import InstallerGuide from './pages/auth/guides/InstallerGuide.vue'
import SubscriptionGroupGuide from './pages/auth/guides/SubscriptionGroupGuide.vue'
import SendBillGuide from './pages/auth/guides/SendBillGuide.vue'
import FilterBySubscriptionUnitGuide from './pages/auth/guides/FilterBySubscriptionUnitGuide.vue'

// dev test pages
import IPayTest from './pages/auth/dev/IPayTest.vue'
import PayTest from './pages/auth/dev/PaymentTest.vue'
import Autocount from './pages/auth/autocount/index.vue'

// landing pages
import Home from './pages/Home.vue'
import About from './pages/About.vue'
import PayLink from './pages/PayLink.vue'
import PayexReturn from './pages/payexreturn.vue'
import PayBill from './pages/PayBill.vue'
import Login from './pages/Login.vue'
import ForgotPass from './pages/ForgotPass.vue'

import Pricing from './pages/Pricing.vue'
import Priviledges from './pages/Priviledges.vue'

import { authStore } from './store/auth-store'
import { auth2Store } from './store/auth2-store'
import Tailwindfix from './pages/auth/dev/tailwindfix.vue'
// import Autocount from './pages/auth/dev/autocount.vue'
import Devlog from './pages/auth/dev/devlog.vue'

export function setupRouter(i18n: I18n): Router {
  const locale: string =
    i18n.mode === 'legacy'
      ? i18n.global.locale
      : ((i18n.global as unknown) as Composer).locale.value

  // setup routes
  const routes: RouteRecordRaw[] = [
    {
      path: '/:locale/',
      component: headerComp,
      children: [
        {
          path: '',
          name: 'home',
          // component: Home
          component: Login,
          meta: {
            auth: false,
          },
        },
        {
          path: 'payexreturn',
          name: 'payexreturn',
          component: PayexReturn,
          // meta: {
          //   auth: false,
          // },
        },
        {
          path: 'paybill/:id',
          name: 'paybill',
          component: PayBill
        },
        // {
        //   path: 'paymentlink/:id',
        //   name: 'upaylink',
        //   component: PayLink,
        // },
        {
          path: 'login',
          name: 'login',
          component: Login,
          meta: {
            auth: false,
          },
        },
        {
          path: 'resetpasswordnow',
          name: 'forgot',
          component: ForgotPass,
          props: (route) => ({ email: route.query.email, resettoken: route.query.resettoken }),
          meta: {
            auth: false,
          },
        },
        /*
        {
          path: 'about',
          name: 'about',
          component: About
        },
        {
          path: '/:pathMatch(.*)*',
          redirect: () => `/${locale}`
        },
        {
          path: 'pricing',
          name: 'pricing',
          component: Pricing
        },
        {
          path: 'priviledges',
          name: 'priviledges',
          component: Priviledges
        },
        */
      ]
    },
    {
      path: '/:locale/auth/printinstallorder/:id',
      meta: { auth: true },
      name: 'printInstallOrder',
      component: PrintInstallOrder
    },
    {
      path: '/:locale/auth/',
      component: adminHeaderComp,
      meta: {
        auth: true,
      },
      children: [
        {
          path: 'dashboard',
          name: 'dashboard',
          component: Dashboard
        },
        {
          path: 'billings',
          name: 'billings',
          component: Billings
        },
        {
          path: 'prebills',
          name: 'prebills',
          component: Prebills
        },
        {
          path: 'ticketsCRUD/:id?',
          name: 'ticketsCRUD',
          component: TicketsCRUD
        },
        {
          path: 'ticketsCustomer',
          name: 'ticketsCustomer',
          component: TicketsCustomer
        },
        {
          path: 'users',
          name: 'users',
          component: Users
        },
        {
          path: 'buildings',
          name: 'buildings',
          component: Buildings
        },
        {
          path: 'subscriptiongroups',
          name: 'subscriptiongroups',
          component: SubscriptionGroups
        },
        {
          path: 'jnxreport',
          name: 'jnxreport',
          component: JnxReport
        },
        {
          path: 'subpayreport',
          name: 'subpayreport',
          component: SubPayReport
        },
        {
          path: 'activeuserreport',
          name: 'activeuserreport',
          component: ActiveUserReport
        },
        {
          path: 'userreport',
          name: 'userreport',
          component: UserReport
        },
        {
          path: 'adhocreport',
          name: 'adhocreport',
          component: AdHocReport
        },
        {
          path: 'subscriptionreport',
          name: 'subscriptionreport',
          component: SubscriptionReport
        },
        {
          path: 'activationreport',
          name: 'activationreport',
          component: ActivationReport
        },
        {
          path: 'agentuserreport',
          name: 'agentuserreport',
          component: AgentUserReport
        },
        {
          path: 'latestbillreport',
          name: 'latestbillreport',
          component: LatestBillReport
        },
        {
          path: 'ticketsummaryreport',
          name: 'ticketsummaryreport',
          component: TicketSummaryReport
        },
        {
          path: 'upcomingreport',
          name: 'upcomingreport',
          component: UpcomingReport
        },
        {
          path: 'summarybills',
          name: 'summarybills',
          component: UpcomingReport
        },
        {
          path: 'ticketreport',
          name: 'ticketreport',
          component: TicketReport
        },
        {
          path: 'upcomingfreeusagereport',
          name: 'upcomingfreeusagereport',
          component: UpcomingFreeUsageReport
        },
        {
          path: 'salessummaryreport',
          name: 'salessummaryreport',
          component: SalesSummaryReport
        },
        {
          path: 'invalidicuserreport',
          name: 'invalidicuserreport',
          component: InvalidIcUserReport
        },
        {
          path: 'billwithagent',
          name: 'billwithagent',
          component: BillWithAgent
        },
        {
          path: 'billbydate',
          name: 'billbydate',
          component: BillByDate
        },
        {
          path: 'billwithagent2',
          name: 'billwithagent2',
          component: BillWithAgent2
        },
        {
          path: 'billbyplan',
          name: 'billbyplan',
          component: BillByPlan
        },
        {
          path: 'zerospecialratereport',
          name: 'zerospecialratereport',
          component: ZeroSpecialRateReport
        },
        {
          path: 'report',
          name: 'report',
          component: Report
        },
        {
          path: 'subscriptiontroubleshoot',
          name:'subscriptiontroubleshoot',
          component: SubscriptionTroubleshoot
        },
        {
          path: 'plans',
          name: 'plans',
          component: Plans
        },
        {
          path: 'subscriptions',
          name: 'subscriptions',
          component: Subscriptions
        },
        {
          path: 'subscriptionsl1/:id?/:ticketId?',
          name: 'subscriptionsl1',
          component: Subscriptionsl1
        },
        {
          path: 'techorder',
          name: 'techorder',
          component: Techorder,
        },
        {
          path: 'dev/ipaytest',
          name: 'ipaytest',
          component: IPayTest
        },
        {
          path: 'dev/paytest',
          name: 'paytest',
          component: PayTest
        },
        {
          path: 'dev/tailwindfix',
          name: 'tailwindfix',
          component: Tailwindfix
        },
        {
          path: 'dev/autocount',
          name: 'autocount',
          component: Autocount
        },
        {
          path: 'dev/devlog',
          name: 'devlog',
          component: Devlog
        },
        {
          path: 'dev/paymentbulk',
          name: 'paymentbulk',
          component:  PaymentBulk
        },
        {
          path: 'dev/emailtools',
          name: 'emailtools',
          component:  EmailTools
        },
        {
          path: 'adminpanel',
          name: 'adminpanel',
          component: AdminPanel
        },
        {
          path: 'dev/useremails',
          name: 'useremails',
          component: () => import('./pages/auth/dev/useremails.vue')
        },
        {
          path: 'idsync',
          name: 'idsync',
          component: IDSync
        },
        {
          path: 'importsubscribers',
          name: 'importsubscribers',
          component: ImportSubscribers
        },
        {
          path: 'faq',
          name: 'faq',
          component: Faq
        },
        {
          path: 'settings',
          name: 'settings',
          component: Settings
        },
        {
          path: 'guides',
          name: 'guides',
          component: Guides
        },
        {
          path: 'consolidate-guide',
          name: 'consolidateGuide',
          component: ConsolidateGuide
        },
        {
          path: 'subscriptionfilterguide',
          name: 'subscriptionfilterguide',
          component: SubscriptionFilterGuide
        },
        {
          path: 'syncBillsGuide',
          name: 'syncBillsGuide',
          component: SyncBillingGuide
        },
        {
          path: 'subscriptionTroubleshootGuide',
          name: 'subscriptionTroubleshootGuide',
          component: SubscriptionTroubleshootGuide
        },
        {
          path: 'customerGuide',
          name: 'customerGuide',
          component: CustomerGuide
        },
        {
          path: 'installerGuide',
          name: 'installerGuide',
          component: InstallerGuide
        },
        {
          path: 'subscriptionGroupGuide',
          name: 'subscriptionGroupGuide',
          component: SubscriptionGroupGuide
        },
        {
          path: 'sendBillGuide',
          name: 'sendBillGuide',
          component: SendBillGuide
        },
        {
          path: 'filterBySubscriptionUnitGuide',
          name: 'filterBySubscriptionUnitGuide',
          component: FilterBySubscriptionUnitGuide
        }
      ]
    }
  ]

  // create router instance
  const router = createRouter({
    history: createWebHistory(),
    routes
  })

  // navigation guards
  router.beforeEach(async to => {
    const paramsLocale = to.params.locale as string
    await authStore.init()

    const auth2State = auth2Store.getState()
    const authState = authStore.getState()
    if (authState.token) {
      if (!auth2Store.hasProfile() || !auth2State.profile || (auth2State.profile && !auth2State.profile.hasOwnProperty('customer') && to.name !== 'home')) {
        await auth2Store.getProfile(authState.token)
      }
    }
    if (to.meta) {
      if (to.meta.auth === false && authState.token) {
      //   // console.log('check user auth')
      //   return `/${paramsLocale || locale}`
      // } else if (to.meta.auth === true && !authState.token) {
      //   return `/${paramsLocale || locale}/login`
      // } else if (!to.fullPath.includes('auth') && authState.token) {
        return `/${paramsLocale || locale}/auth/dashboard`
      } else if (to.meta.auth === true && !authState.token) {
        return `/${paramsLocale || locale}/login`
      }
    }

    // use locale if paramsLocale is not in SUPPORT_LOCALES
    if (!SUPPORT_LOCALES.includes(paramsLocale)) {
      return `/${locale}`
    }

    // load locale messages
    if (!i18n.global.availableLocales.includes(paramsLocale)) {
      await loadLocaleMessages(i18n, paramsLocale)
    }

    // set i18n language
    setI18nLanguage(i18n, paramsLocale)
  })

  return router
}
