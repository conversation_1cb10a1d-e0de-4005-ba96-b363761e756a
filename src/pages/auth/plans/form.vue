<template>
  <PopupModal
      defaultColor="blue"
      modalWidthPercent="90"
      :title="$t('plans.formTitle')"
      :btnYesText="$t('c.submit')"
      :btnYesFunction="submitNow"
      :btnNoText="$t('c.cancel')"
      :btnNoFunction="cancel"
      v-if="item">
    <form v-if="!refreshList" @submit.prevent="preventsubmit" autocomplete="">
      <div class="text-right"><div class="text-xs p-1 px-2 rounded bg-gray-200 inline-block">{{item.id}}</div></div>
      <FormItemCompt
          class="w-full inline-block px-1"
          labelFor="planid"
          itemValue="srvid"
          :labelTitle="$t('plans.planid')"
          :required="true">
        <SelectOne
            :list="planidList"
            :selectedItem="item.planid"
            showTitle="srvname"
            itemValue="srvid"
            :addOrRemoveFunction="addRemovePlanID"
            defaultColor="blue"></SelectOne>
        <ErrTextCompt :errs="errs && errs.planid"></ErrTextCompt>
      </FormItemCompt>
      <div>      
        <FormItemCompt
            class="w-full lg:w-3/4 inline-block px-1"
            labelFor="title"
            :labelTitle="$t('plans.title')"
            :required="true">
          <TextInput
              name="title"
              v-model="item.title"
              :placeholder="$t('c.frontPlc') + $t('plans.title')"
              defaultColor="blue"></TextInput>
          <ErrTextCompt :errs="errs && errs.title"></ErrTextCompt>
        </FormItemCompt>
        <div class="w-full lg:w-1/4 inline-block px-1">
          <FormItemCompt
            class="inline-block sm:w-1/2"
                labelFor="hide"
                labelTitle="">
                <Checkbox v-model="item.hide" :label="$t('plans.hide')" defaultColor="blue"></Checkbox>
          </FormItemCompt>
          <FormItemCompt
            class="inline-block sm:w-1/2"
                labelFor="voip"
                labelTitle="">
                <Checkbox v-model="item.voip" :label="$t('plans.voip')" defaultColor="blue"></Checkbox>
          </FormItemCompt>
        </div>
      </div>
      <FormItemCompt
          class="w-full md:w-3/4 inline-block px-1 align-top"
          labelFor="details"
          :labelTitle="$t('plans.details')">
        <TextareaInput
            name="details"
            v-model="item.details"
            :placeholder="$t('c.frontPlc') + $t('plans.details')"
            :rows="6"
            defaultColor="blue"></TextareaInput>
        <ErrTextCompt :errs="errs && errs.details"></ErrTextCompt>
      </FormItemCompt>
      <div>
        <FormItemCompt
            class="w-full lg:w-1/6 inline-block px-1"
            labelFor="contract"
            :labelTitle="$t('plans.contract')"
            :required="true">
          <TextInput
              type="number"
              name="contract"
              v-model="item.contract"
              :placeholder="$t('c.frontPlc') + $t('plans.contract')"
              defaultColor="blue"></TextInput>
          <ErrTextCompt :errs="errs && errs.contract"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full lg:w-1/6 inline-block px-1"
            labelFor="freedays"
            :labelTitle="$t('plans.freedays')"
            :required="true">
          <TextInput
              type="number"
              name="freedays"
              v-model="item.freedays"
              :placeholder="$t('c.frontPlc') + $t('plans.freedays')"
              defaultColor="blue"></TextInput>
          <ErrTextCompt :errs="errs && errs.freedays"></ErrTextCompt>
        </FormItemCompt><FormItemCompt
            class="w-full lg:w-1/6 inline-block px-1"
            labelFor="price"
            :labelTitle="$t('plans.price')"
            :required="true">
          <TextInput
              type="number"
              name="price"
              v-model="item.price"
              :placeholder="$t('c.frontPlc') + $t('plans.price')"
              defaultColor="blue"></TextInput>
          <ErrTextCompt :errs="errs && errs.price"></ErrTextCompt>
        </FormItemCompt>
        <FormItemCompt
            class="w-full lg:w-1/6 inline-block px-1"
            labelFor="tax"
            :labelTitle="`${$t('plans.tax')} %`">
          <TextInput
              type="number"
              name="tax"
              v-model="item.tax"
              :placeholder="$t('c.frontPlc') + $t('plans.tax')"
              defaultColor="blue"></TextInput>
          <ErrTextCompt :errs="errs && errs.tax"></ErrTextCompt>
        </FormItemCompt>
        <div class="text-xs text-right">{{$t('c.total')}} : {{priceTax}}</div>
      </div>
      <div v-if="!item.voip" class="text-right my-3"><div @click="showmorefunc" class="bg-gray-300 rounded px-2 py-1 text-xs inline-block cursor-pointer">{{$t('c.more')}}</div></div>
      <div class="rounded border p-4 mb-5" v-if="showmore && !item.voip">
        <div class="grid grid-cols-4">
          <FormItemCompt
            class="px-1"
            labelFor="price2"
            :labelTitle="$t('plans.price2')"
            :required="false">
            <TextInput
                type="number"
                name="price2"
                v-model="item.price2"
                :placeholder="$t('c.frontPlc') + $t('plans.price')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.price2"></ErrTextCompt>
          </FormItemCompt>
       
          <FormItemCompt
            class="px-1"
            labelFor="price3"
            :labelTitle="$t('plans.price3')"
            :required="false">
            <TextInput
                type="number"
                name="price3"
                v-model="item.price3"
                :placeholder="$t('c.frontPlc') + $t('plans.price')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.price3"></ErrTextCompt>
          </FormItemCompt>

          <FormItemCompt
            class="px-1"
            labelFor="price4"
            :labelTitle="$t('plans.price4')"
            :required="false">
            <TextInput
                type="number"
                name="price4"
                v-model="item.price4"
                :placeholder="$t('c.frontPlc') + $t('plans.price')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.price4"></ErrTextCompt>
          </FormItemCompt>

          <FormItemCompt
            class="px-1"
            labelFor="price5"
            :labelTitle="$t('plans.price5')"
            :required="false">
            <TextInput
                type="number"
                name="price5"
                v-model="item.price5"
                :placeholder="$t('c.frontPlc') + $t('plans.price')"
                defaultColor="blue"></TextInput>
            <ErrTextCompt :errs="errs && errs.price5"></ErrTextCompt>
          </FormItemCompt>
        </div>
      </div>
      <FormItemCompt
          class="w-full inline-block px-1"
          labelFor="images"
          :labelTitle="$t('plans.images')"
          :required="true">
        <div>
            <ShowFiles :files="images" :removeFile="removeFile" />
        </div>
        <UploadComp
            :addFile="addFile"
            :basePath="basePath"
            :token="token" >
        </UploadComp>
        <ErrTextCompt :errs="errs && errs.images"></ErrTextCompt>
      </FormItemCompt>
      <FormItemCompt
          class="w-full md:w-1/2 inline-block px-1"
          labelFor="startdate"
          :labelTitle="$t('plans.startdate')"
          :required="true">
        <DatePicker
            v-model="item.startdate"
            :clearable="true"
            defaultColor="blue"></DatePicker>
        <ErrTextCompt :errs="errs && errs.startdate"></ErrTextCompt>
      </FormItemCompt>
      <FormItemCompt
          class="w-full md:w-1/2 inline-block px-1"
          labelFor="enddate"
          :labelTitle="$t('plans.enddate')"
          :required="true">
        <DatePicker
            v-model="item.enddate"
            :clearable="true"
            defaultColor="blue"></DatePicker>
        <ErrTextCompt :errs="errs && errs.enddate"></ErrTextCompt>
      </FormItemCompt>
    </form>
  </PopupModal>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import PopupModal from '@/components/cvui/Modal.vue'
import FormItemCompt from '@/components/cvui/form/FormItem.vue'
import TextInput from '@/components/cvui/form/TextInput.vue'
import TextareaInput from '@/components/cvui/form/TextareaInput.vue'
import SelectList from '@/components/cvui/form/SelectList.vue'
import Checkbox from '@/components/cvui/form/Checkbox.vue'
import DatePicker from '@/components/cvui/form/DatePicker.vue'
import SelectOne from '@/components/cvui/form/SelectOne.vue'
import ErrTextCompt from '@/components/cvui/form/ErrText.vue'
import { getPlanDMA, getFile, deleteFile, basePath } from '../../../api'
export default defineComponent({
  props: {
    item: { type: Object, required: true },
    cancel: { type: Function, required: true },
    save:  { type: Function, required: true },
    saveSilent:  { type: Function, required: true },
    token: { type: String, required: true }
  },
  components: {
    PopupModal,
    FormItemCompt,
    TextInput,
    TextareaInput,
    SelectList,
    Checkbox,
    DatePicker,
    ErrTextCompt,
    SelectOne
  },
  computed: {
    priceTax () {
      let price: string = this.item.price
      let tax: string = this.item.tax
      return (parseFloat(price) + (parseFloat(price) * (parseFloat(tax) / 100))).toFixed(2)
    },
  },
  mounted () {
    this.getPlanID(1, 5, '')
  },
  methods: {
    showmorefunc () {
      this.showmore = !this.showmore
    },
    getPlanID (pg:any, pageSize:any, keywords:any) {
      var skip = (pg && pg > 1) ? ((pg - 1) * pageSize) : 0
      getPlanDMA({ token: this.token, skip: skip, limit: pageSize, keywords: keywords }).then(res => {
        var d: any = res
        if (d.data) {
          this.planidList = this.planidList.concat(d.data)
          if ((d.total / pageSize) >= pg) {
            this.getPlanID(pg + 1, pageSize, keywords)
          }
        }
      })
    },
    addRemovePlanID (item: any, index: any) {
      if (this.item.planid === item.srvid) {
        this.item.planid = ''
        this.item.title = ''
        this.item.details = ''
        this.item.price = 0
        this.item.tax = 6
      } else {
        this.item.planid = item.srvid
        this.item.title = item.srvname
        this.item.details = item.descr
        this.item.price = item.unitprice
        this.item.tax = 6
      }
      this.refreshList = true
      this.$nextTick(() => {
        this.refreshList = false
      })
    },
    addFile (p: any) {
      if (!this.item.images) {
      this.item.images = []
      }
      this.item.images.push(p.file.id)
      this.images.push(p.file)
      this.saveSilent(this.item)
    },
    removeFile (p: any) {
      let i = this.item.images.indexOf(p)
      if (i > -1) {
      this.item.images.splice(i, 1)
      this.saveSilent(this.item)
      }
      let list = this.images.map((p: any) => p.id || p.ID)
      let j = list.indexOf(p)
      this.images.splice(j, 1)
      deleteFile({id: p, token: this.token})
    },
    validateForm () {
      let p = true
      if (!this.item.planid || (this.item.planid && this.item.planid.toString().trim().length == 0)) {
        this.errs['planid'] = 'c.fieldRequired'
        p = false
      }
      if (!this.item.price || this.item.price <= 0) {
        this.errs['price'] = 'c.fieldRequired'
        p = false
      }
      // if (!this.item.tax || this.item.tax <= 0) {
      //   this.errs['tax'] = 'c.fieldRequired'
      //   p = false
      // }
      // if (!this.item.images || this.item.images.length <= 0) {
      //   this.errs['images'] = 'c.fieldRequired'
      //   p = false
      // }
      if (!this.item.startdate || this.item.startdate.toString().trim().length == 0) {
        this.errs['startdate'] = 'c.fieldRequired'
        p = false
      }
      if (!this.item.enddate || this.item.enddate.toString().trim().length == 0) {
        this.errs['enddate'] = 'c.fieldRequired'
        p = false
      }
      if (this.item.price) {
        this.item.price = parseFloat(this.item.price)
      }
      if (this.item.price2) {
        this.item.price2 = parseFloat(this.item.price2)
      }
      if (this.item.price3) {
        this.item.price3 = parseFloat(this.item.price3)
      }
      if (this.item.price4) {
        this.item.price4 = parseFloat(this.item.price4)
      }
      if (this.item.price5) {
        this.item.price5 = parseFloat(this.item.price5)
      }
      if (this.item.tax) {
        this.item.tax = parseFloat(this.item.tax)
      }
      if (this.item.contract) {
        this.item.contract = parseInt(this.item.contract)
      }
      if (this.item.freedays) {
        this.item.freedays = parseInt(this.item.freedays)
      }
      return p
    },
    preventsubmit (e: any) {
      e.preventDefault()
    },
    submitNow (e: any) {
      // e.preventDefault()
      if (this.validateForm()) {
        this.save(this.item)
        this.cancel()
      }
      return false
    }
  },
  data () {
    let errs: any = {}
    let planidList: any = []
    let images: any = []
    let pp: string = `${String(basePath)}api/upload`
    let refreshList: boolean = false
    let showmore: boolean = false
    return {
      errs,
      planidList,
      images,
      basePath: pp,
      refreshList,
      showmore,
    }
  }
})
</script>
